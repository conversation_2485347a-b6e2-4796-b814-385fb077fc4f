"use client"; 

import React, { useEffect, useRef, useState, useCallback } from "react"; 
import { fabric } from 'fabric'; 
import { Button } from "@/components/ui/button"; 
import { Input } from "@/components/ui/input"; 
import { Label } from "@/components/ui/label"; 
import { Checkbox } from "@/components/ui/checkbox"; 
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; 
import { TextBlock } from "@/types/pdf"; 
import { FONT_MAP, fontKey } from "@/utils/fontMapper"; 
import * as pdfjsLib from "pdfjs-dist"; 
import { PDFDocument, rgb, degrees } from 'pdf-lib'; 
import { Save, Trash2, PlusSquare } from 'lucide-react';
import fontkit from '@pdf-lib/fontkit';

pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js'; 

interface PDFEditorProps { 
  textBlocks: TextBlock[]; 
  blankPdfBytes: Uint8Array;
  originalPdfBytes: Uint8Array;
  onExport: (pdfBytes: Uint8Array) => void; 
  selectedPdfName: string; 
  targetLanguage: string; 
} 

export default function PDFEditor({ textBlocks: initialTextBlocks, blankPdfBytes, originalPdfBytes, onExport, selectedPdfName, targetLanguage }: PDFEditorProps) { 
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const originalCanvasRef = useRef<HTMLCanvasElement>(null); 
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [originalCanvas, setOriginalCanvas] = useState<fabric.Canvas | null>(null);
  const [selectedObject, setSelectedObject] = useState<fabric.IText | null>(null);
  const [exportTrigger, setExportTrigger] = useState(false);
  const [loadedFonts, setLoadedFonts] = useState<Set<string>>(new Set());
  const [originalPdfDoc, setOriginalPdfDoc] = useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });
  // useRef untuk menyimpan salinan asli blankPdfBytes
  const originalPdfBytesRef = useRef<Uint8Array | null>(null); 
  // useRef untuk menyimpan ArrayBuffer font kustom untuk pdf-lib
  const pdfLibFontBytesRef = useRef<Record<string, ArrayBuffer>>({});

  // Toolbar state untuk sinkronisasi real-time 
  const [toolbarState, setToolbarState] = useState({ 
    fontSize: 12, 
    fontFamily: 'Helvetica', 
    fontWeight: 'normal', 
    fontStyle: 'normal', 
    fill: '#000000', 
    isOriginalText: true 
  }); 

  const [pdfDoc, setPdfDoc] = useState<pdfjsLib.PDFDocumentProxy | null>(null); 
  const [pdfPage, setPdfPage] = useState<pdfjsLib.PDFPageProxy | null>(null); 
  const [currentPage, setCurrentPage] = useState(1); 
  const [totalPages, setTotalPages] = useState(0); 
  const [pageInput, setPageInput] = useState<string>('1'); 

  // State baru untuk menyimpan semua textBlocks, termasuk yang sudah diedit 
  const [allTextBlocks, setAllTextBlocks] = useState<TextBlock[]>(initialTextBlocks); 

  // Perbarui allTextBlocks ketika initialTextBlocks dari props berubah 
  useEffect(() => { 
    setAllTextBlocks(initialTextBlocks); 
  }, [initialTextBlocks]); 

  // Fungsi untuk memperbarui toolbar state berdasarkan objek yang dipilih
  const updateToolbarState = useCallback((obj: fabric.IText | null) => {
    if (obj) {
      const originalText = (obj.data as any)?.originalText;
      const currentText = obj.text;
      const isOriginalText = currentText === originalText;

      // Gunakan fabric.Color untuk mem-parse warna, lalu konversi ke hex
      const color = new fabric.Color(obj.fill as string || '#000000');
      const source = color.getSource(); // [r, g, b, a]
      const hexColor = rgbToHex({ r: source[0] / 255, g: source[1] / 255, b: source[2] / 255 });

      // Untuk font custom, kita perlu menentukan weight dan style yang sebenarnya
      // berdasarkan nama font file, bukan dari Fabric.js properties
      const fontFamily = obj.fontFamily || 'Helvetica';
      let actualWeight = 'normal';
      let actualStyle = 'normal';

      if (fontFamily.includes('MuseoSans-700')) {
        actualWeight = 'bold';
      } else if (fontFamily.includes('MuseoSans-300')) {
        actualWeight = 'normal';
      } else if (fontFamily.includes('MuseoSans-500')) {
        actualWeight = '500';
      } else if (fontFamily.includes('MuseoSans-900')) {
        actualWeight = '900';
      } else {
        // Untuk font non-custom, gunakan Fabric.js properties
        actualWeight = typeof obj.fontWeight === 'number' ? obj.fontWeight.toString() : (obj.fontWeight || 'normal');
      }

      if (fontFamily.includes('Italic')) {
        actualStyle = 'italic';
      } else {
        // Untuk font non-custom, gunakan Fabric.js properties
        actualStyle = obj.fontStyle || 'normal';
      }

      setToolbarState({
        fontSize: obj.fontSize || 12,
        fontFamily: fontFamily,
        fontWeight: actualWeight,
        fontStyle: actualStyle,
        fill: hexColor, // Selalu dalam format #rrggbb
        isOriginalText
      });
    } else {
      setToolbarState({
        fontSize: 12,
        fontFamily: 'Helvetica',
        fontWeight: 'normal',
        fontStyle: 'normal',
        fill: '#000000',
        isOriginalText: true
      });
    }
  }, []);

  // Fungsi untuk menambah textbox baru
  const addTextbox = () => {
    if (!canvas) return;

    const newText = new fabric.IText('Teks Baru', {
      left: canvas.width ? canvas.width / 2 : 100,
      top: canvas.height ? canvas.height / 2 : 100,
      fontFamily: 'MuseoSans-300', // Gunakan file font yang tepat
      fontSize: 13,
      fill: '#000000',
      // Karena menggunakan MuseoSans-300 (font file khusus), set ke normal
      fontWeight: 'normal',
      fontStyle: 'normal',
      data: {
        id: `new-${Date.now()}`, // ID unik untuk objek baru
        originalText: 'Teks Baru',
        translatedText: 'Teks Baru',
      }
    });
    canvas.add(newText);
    canvas.setActiveObject(newText);
    canvas.renderAll();
    saveCurrentPageEdits(currentPage);
  };

  // Fungsi untuk menghapus objek yang dipilih
  const deleteSelectedObject = () => {
    if (selectedObject && canvas) {
      canvas.remove(selectedObject);
      setSelectedObject(null);
      updateToolbarState(null);
      canvas.renderAll();
      saveCurrentPageEdits(currentPage);
    }
  }; 

  // Fungsi untuk memuat font untuk Fabric.js (browser rendering)
  const loadFont = async (fontName: string, fontPath: string) => { 
    if (loadedFonts.has(fontName)) return; 

    try { 
      const font = new FontFace(fontName, `url(${fontPath})`); 
      await font.load(); 
      document.fonts.add(font); 
      setLoadedFonts(prev => new Set([...prev, fontName])); 
      console.log(`Font ${fontName} loaded successfully for Fabric.js`); 
    } catch (error) { 
      console.error(`Failed to load font ${fontName} for Fabric.js:`, error); 
    } 
  }; 

  // Load semua font untuk Fabric.js saat komponen dimount 
  useEffect(() => { 
    const loadAllFonts = async () => { 
      const fontPromises = Object.entries(FONT_MAP).map(([fontName, fontPath]) => 
        loadFont(fontName, fontPath) 
      ); 
      await Promise.all(fontPromises); 
    }; 
    loadAllFonts(); 
  }, []); 

  // Load font files sebagai ArrayBuffer untuk pdf-lib embedding
  useEffect(() => {
    const loadPdfLibFonts = async () => {
      const fontPromises = Object.entries(FONT_MAP).map(async ([fontName, fontPath]) => {
        try {
          const response = await fetch(fontPath);
          const fontBytes = await response.arrayBuffer();
          pdfLibFontBytesRef.current[fontName] = fontBytes;
          console.log(`PDF-Lib font bytes loaded for ${fontName}`);
        } catch (error) {
          console.error(`Failed to fetch PDF-Lib font bytes for ${fontName}:`, error);
        }
      });
      await Promise.all(fontPromises);
    };
    loadPdfLibFonts();
  }, []); // Jalankan sekali saat komponen dimount

  // Mendapatkan daftar font yang tersedia
  const getAvailableFonts = useCallback(() => {
    const fonts = [
      { value: "Helvetica", label: "Helvetica (Default)" },
      { value: "Arial", label: "Arial" },
      { value: "Times New Roman", label: "Times New Roman" },
    ];

    // Hanya tampilkan nama font dasar, bukan varian weight/italic
    const baseFonts = new Set<string>();
    Object.keys(FONT_MAP).forEach(fontName => {
      // Extract base font name (e.g., "MuseoSans" from "MuseoSans-300")
      const baseName = fontName.split('-')[0];
      baseFonts.add(baseName);
    });

    // Tambahkan font dasar ke dropdown
    baseFonts.forEach(baseName => {
      fonts.push({ value: baseName, label: baseName });
    });

    return fonts;
  }, []);

  // Fungsi untuk menyimpan objek kanvas saat ini ke state `allTextBlocks`
const saveCurrentPageEdits = useCallback(async (pageToSave: number) => {
  return new Promise<TextBlock[]>((resolve) => {
    if (!canvas) {
      console.log("saveCurrentPageEdits: Canvas is null, resolving immediately.");
      return;
    }

    const currentCanvasObjects = canvas.getObjects().filter(obj => obj.type === 'i-text') as fabric.IText[];

    setAllTextBlocks(prevBlocks => {
      const blocksWithoutCurrentPage = prevBlocks.filter(block => block.pageNumber !== pageToSave);

      const scale = 1.0;

      const updatedBlocksForCurrentPage: TextBlock[] = currentCanvasObjects.map(obj => {
        const originalBlock = prevBlocks.find(b =>
          (b.id === (obj.data as any)?.id || (obj.data as any)?.isNew) && b.pageNumber === pageToSave
        );

        const calculatedWidth = obj.getScaledWidth();
        const calculatedHeight = obj.getScaledHeight();
        const lineHeight = (obj.lineHeight || 1.1) * (obj.fontSize || 12);

        // Deteksi weight dan italic yang sebenarnya dari fontFamily
        const fontFamily = obj.fontFamily || 'Helvetica';
        let actualWeight = 'normal';
        let actualItalic = false;

        if (fontFamily.includes('MuseoSans-700')) {
          actualWeight = 'bold';
        } else if (fontFamily.includes('MuseoSans-500')) {
          actualWeight = '500';
        } else if (fontFamily.includes('MuseoSans-900')) {
          actualWeight = '900';
        } else if (fontFamily.includes('MuseoSans-300')) {
          actualWeight = 'normal';
        } else {
          // Untuk font non-custom, gunakan Fabric.js properties
          actualWeight = typeof obj.fontWeight === 'number' ? obj.fontWeight.toString() : (obj.fontWeight || 'normal');
        }

        if (fontFamily.includes('Italic')) {
          actualItalic = true;
        } else {
          // Untuk font non-custom, gunakan Fabric.js properties
          actualItalic = obj.fontStyle === 'italic';
        }

        return {
          id: (obj.data as any)?.id || `new-${Date.now()}-${Math.random().toString(36).substring(7)}`,
          pageNumber: pageToSave,
          text: (obj.data as any)?.originalText || obj.text || '',
          translated: obj.text || '',
          x: (obj.left || 0) / scale,
          y: (obj.top || 0) + (obj.fontSize || 0),

          size: (obj.fontSize || 12) / scale,
          font: originalBlock?.font || 'Helvetica',
          fontFamily: fontFamily,
          fontWeight: actualWeight,
          italic: actualItalic,
          rotation: -(obj.angle || 0),
          color: colorToRgb(obj.fill as string),
          width: calculatedWidth / scale,
          height: calculatedHeight / scale,
          lineHeight: lineHeight / scale,
        };
      });

      const newAllTextBlocks = [...blocksWithoutCurrentPage, ...updatedBlocksForCurrentPage];

      console.log(`[saveCurrentPageEdits] Finished processing. allTextBlocks for page ${pageToSave}:`, newAllTextBlocks.filter(b => b.pageNumber === pageToSave));
      console.log(`[saveCurrentPageEdits] Total allTextBlocks count: ${newAllTextBlocks.length}`);

      resolve(newAllTextBlocks);
      return newAllTextBlocks;
    });
  });
}, [canvas]); // currentPage dihapus dari dependensi useCallback karena sudah dipass sebagai argumen

  // Render halaman tertentu 
  const renderPage = async (doc: pdfjsLib.PDFDocumentProxy, pageNum: number) => { 

    const page = await doc.getPage(pageNum); 
    setPdfPage(page); 

    const scale = 1.0; 
    const viewport = page.getViewport({ scale }); 

    const canvasEl = canvasRef.current; 
    if (!canvasEl) return; 

    if (canvas) { 
      canvas.dispose(); 
      setCanvas(null); 
    } 

    canvasEl.width = viewport.width; 
    canvasEl.height = viewport.height; 

    const fabricCanvas = new fabric.Canvas(canvasEl, { 
      width: viewport.width, 
      height: viewport.height, 
      backgroundColor: 'transparent', 
      selection: true, 
      preserveObjectStacking: true, 
    }); 

    const tempCanvas = document.createElement('canvas'); 
    const tempCtx = tempCanvas.getContext('2d')!; 

    // Gunakan scale yang lebih rendah untuk background preview 
    const backgroundScale = 1.0; 
    const backgroundViewport = page.getViewport({ scale: backgroundScale }); 

    tempCanvas.width = backgroundViewport.width; 
    tempCanvas.height = backgroundViewport.height; 

    const renderContext = { 
      canvasContext: tempCtx, 
      DIPRatio: 1, 
      viewport: backgroundViewport, 
    }; 

    await page.render(renderContext).promise; 

    const pdfImageSrc = tempCanvas.toDataURL('image/png'); 

    fabric.Image.fromURL(pdfImageSrc, (img) => { 
      if (img && img.width && img.height && fabricCanvas && fabricCanvas.width && fabricCanvas.height) { 
        fabricCanvas.setBackgroundImage(img, fabricCanvas.renderAll.bind(fabricCanvas), { 
          scaleX: fabricCanvas.width / img.width, 
          scaleY: fabricCanvas.height / img.height, 
          originX: 'left', 
          originY: 'top', 
        }); 
      } else { 
        console.error("Failed to load PDF background image or image dimensions are undefined."); 
      } 
    }); 

    const blocksForPage = allTextBlocks.filter((b) => b.pageNumber === pageNum); 

    blocksForPage.forEach((block) => {
      // Menggunakan fontKey dari utils/fontMapper.ts untuk mendapatkan nama font yang benar untuk Fabric.js
      const mappedFontKey = fontKey(block.fontFamily, block.fontWeight, block.italic);
      // Memastikan font yang digunakan di Fabric.js adalah salah satu yang dimuat atau default
      const mappedFontFamily = FONT_MAP[mappedFontKey] ? mappedFontKey : (block.fontFamily || "Helvetica");

      // Di bagian renderPage, saat membuat fabric.IText:
      const text = new fabric.IText(block.translated || block.text, {
        left: block.x * scale,
        top: (block.y - block.size) * scale,
        fontSize: block.size * scale,
        fontFamily: mappedFontFamily,
        // Jika menggunakan font file khusus (seperti MuseoSans-700), set ke normal
        // agar tidak ada double bold/italic dari Fabric.js
        fontWeight: FONT_MAP[mappedFontKey] ? "normal" : (block.fontWeight === "bold" ? "bold" : "normal"),
        fontStyle: FONT_MAP[mappedFontKey] ? "normal" : (block.italic ? "italic" : "normal"),
        fill: rgbToHex(block.color),
        angle: -(block.rotation) || 0,
        originX: "left",
        originY: "top",
        lineHeight: 1.1, // Atur lineHeight yang sesuai (1.1 adalah nilai default yang baik)
        data: {
          id: block.id,
          originalText: block.text,
          translatedText: block.translated,
        },
      });
      fabricCanvas.add(text);
    });
    console.log(`Objects on Fabric.js Canvas for page ${pageNum}:`, fabricCanvas.getObjects());
    fabricCanvas.on("selection:created", (e) => { 
      const obj = e.selected?.[0]; 
      if (obj && obj.type === "i-text") { 
        const textObj = obj as fabric.IText; 
        setSelectedObject(textObj); 
        updateToolbarState(textObj); 
      } 
    }); 

    fabricCanvas.on("selection:updated", (e) => { 
      const obj = e.selected?.[0]; 
      if (obj && obj.type === "i-text") { 
        const textObj = obj as fabric.IText; 
        setSelectedObject(textObj); 
        updateToolbarState(textObj); 
      } 
    }); 

    fabricCanvas.on("selection:cleared", () => { 
      setSelectedObject(null); 
      updateToolbarState(null); 
    }); 

    // Event handler untuk memperbarui toolbar state secara real-time 
    fabricCanvas.on("object:modified", (e) => { 
      if (e.target && e.target.type === "i-text") { 
        updateToolbarState(e.target as fabric.IText); 
      } 
    }); 

    fabricCanvas.on("text:changed", (e) => { 
      if (e.target && e.target.type === "i-text") { 
        updateToolbarState(e.target as fabric.IText); 
      }
    }); 

    fabricCanvas.on("object:moving", () => { 
    }); 

    fabricCanvas.on("object:scaling", (e) => { 
      if (e.target && e.target.type === "i-text") { 
        updateToolbarState(e.target as fabric.IText); 
      } 
    }); 

    fabricCanvas.on("object:rotating", () => {  
    }); 

    fabricCanvas.on('mouse:dblclick', (options) => { 
      if (options.target && options.target.type === 'i-text') { 
        const textObject = options.target as fabric.IText; 
        textObject.enterEditing(); 
        textObject.setCoords(); 
        fabricCanvas.renderAll(); 
      } 
    }); 

    setCanvas(fabricCanvas); 
  }; 
  // Fungsi untuk render PDF asli
 const renderOriginalPage = async (doc: pdfjsLib.PDFDocumentProxy, pageNum: number) => {
  try {
    const page = await doc.getPage(pageNum);
    const scale = 1.0;
    const viewport = page.getViewport({ scale });

    const canvasEl = originalCanvasRef.current;
    if (!canvasEl) return;

    if (originalCanvas) {
      originalCanvas.dispose();
      setOriginalCanvas(null);
    }

    canvasEl.width = viewport.width;
    canvasEl.height = viewport.height;

    const fabricCanvas = new fabric.Canvas(canvasEl, {
      width: viewport.width,
      height: viewport.height,
      backgroundColor: 'transparent',
      selection: false,
      interactive: false
    });

    // Render ke canvas temporary dengan scale lebih rendah
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d')!;
    const backgroundScale = 1.0;
    const backgroundViewport = page.getViewport({ scale: backgroundScale });
    
    tempCanvas.width = backgroundViewport.width;
    tempCanvas.height = backgroundViewport.height;

    await page.render({
      canvasContext: tempCtx,
      viewport: backgroundViewport
    }).promise;

    const pdfImageSrc = tempCanvas.toDataURL('image/png');

    fabric.Image.fromURL(pdfImageSrc, (img) => {
      if (img && img.width && img.height && fabricCanvas && fabricCanvas.width && fabricCanvas.height) {
        fabricCanvas.setBackgroundImage(img, fabricCanvas.renderAll.bind(fabricCanvas), {
          scaleX: fabricCanvas.width / img.width,
          scaleY: fabricCanvas.height / img.height,
          originX: 'left',
          originY: 'top',
        });
      }
    });

    setOriginalCanvas(fabricCanvas);
  } catch (error) {
    console.error('Error rendering original page:', error);
  }
};
  // Load PDF dokumen 
  useEffect(() => { 
    const loadPdf = async () => { 
      // Simpan salinan asli blankPdfBytes hanya sekali 
      if (!originalPdfBytesRef.current) { 
        originalPdfBytesRef.current = new Uint8Array(blankPdfBytes); 
        console.log("Original blankPdfBytes stored in ref.");
      }

      const doc = await pdfjsLib.getDocument({ data: blankPdfBytes }).promise; 
      setPdfDoc(doc); 
      setTotalPages(doc.numPages); 
      setCurrentPage(1); 
      setPageInput('1'); 
      renderPage(doc, 1);

            // Load PDF asli
      const originalDoc = await pdfjsLib.getDocument({ data: originalPdfBytes }).promise;
      setOriginalPdfDoc(originalDoc);
      renderOriginalPage(originalDoc, 1);
    }; 
    loadPdf(); 

    return () => { 
      if (canvas) canvas.dispose();
      if (originalCanvas) originalCanvas.dispose();
    }; 
  }, [blankPdfBytes, originalPdfBytes]); // Dependensi blankPdfBytes agar useEffect ini berjalan jika prop berubah

  const changePage = async (pageNum: number) => { 
    if (pageNum >= 1 && pageNum <= totalPages && pdfDoc && originalPdfDoc) { 
      setSelectedObject(null); 
      updateToolbarState(null);
      const previousPage = currentPage;  
      setCurrentPage(pageNum); 
      setPageInput(String(pageNum));
      await saveCurrentPageEdits(previousPage); 
      renderPage(pdfDoc, pageNum);
      renderOriginalPage(originalPdfDoc, pageNum);
    }
  }; 

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => { 
    setPageInput(e.target.value); 
  }; 

  const handlePageInputBlur = () => { 
    const pageNum = parseInt(pageInput, 10); 
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) { 
      changePage(pageNum); 
    } else { 
      setPageInput(String(currentPage)); 
    } 
  }; 

  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => { 
    if (e.key === 'Enter') { 
      handlePageInputBlur(); 
    } 
  }; 

  const updateSelectedText = useCallback(<K extends keyof fabric.IText>(key: K, value: fabric.IText[K]) => { 
    if (!selectedObject || !canvas) return; 

    selectedObject.set(key, value); 
    canvas.renderAll(); 
     
    // Update toolbar state immediately 
    updateToolbarState(selectedObject); 
    saveCurrentPageEdits(currentPage); 
  }, [selectedObject, canvas, updateToolbarState, saveCurrentPageEdits]); 

  // Fungsi untuk menangani perubahan font size dengan validasi 
  const handleFontSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => { 
    const value = e.target.value; 
     
    // Izinkan field kosong sementara (untuk editing) 
    if (value === '') { 
      setToolbarState(prev => ({ ...prev, fontSize: 0 })); 
      return; 
    } 

    const numValue = Number(value); 
    if (numValue > 0) { 
      setToolbarState(prev => ({ ...prev, fontSize: numValue })); 
      updateSelectedText("fontSize", numValue); 
    } 
  }; 

  // Fungsi untuk menangani blur pada font size input 
  const handleFontSizeBlur = () => { 
    if (toolbarState.fontSize <= 0) { 
      const defaultSize = 12; 
      setToolbarState(prev => ({ ...prev, fontSize: defaultSize })); 
      updateSelectedText("fontSize", defaultSize); 
    } 
  }; 

  // Fungsi untuk beralih antara teks asli dan terjemahan 
  const toggleTextVersion = useCallback(() => { 
    if (selectedObject && canvas) { 
      const originalText = (selectedObject.data as any)?.originalText; 
      const translatedText = (selectedObject.data as any)?.translatedText; 

      if (selectedObject.text === originalText) { 
        selectedObject.set({ text: translatedText || '' }); 
      } else { 
        selectedObject.set({ text: originalText || '' }); 
      } 
      canvas.renderAll(); 
      updateToolbarState(selectedObject); 
      saveCurrentPageEdits(currentPage); 
    } 
  }, [selectedObject, canvas, updateToolbarState, saveCurrentPageEdits]); 

  // Fungsi untuk mengonversi objek warna {r, g, b} (nilai 0-1) ke string hex #rrggbb
  const rgbToHex = (color: { r: number; g: number; b: number; }) => {
    const toHex = (c: number) => {
      const hex = Math.round(c * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`.toLowerCase();
  };

  // Fungsi untuk mengonversi string warna (hex, rgb, dll.) ke objek RGB
  const colorToRgb = (color: string) => {
    // Gunakan fabric.Color untuk menangani semua format warna yang valid
    const colorObj = new fabric.Color(color || '#000000');
    const source = colorObj.getSource();
    return {
      r: source[0] / 255,
      g: source[1] / 255,
      b: source[2] / 255,
    };
  }; 

  // Helper function to get the correct FONT_MAP key for pdf-lib embedding
  const getPdfLibFontKey = useCallback((block: TextBlock) => {
    const { fontFamily, fontWeight, italic } = block;
    let key = '';

    // Jika fontFamily sudah berupa nama file font yang lengkap (seperti MuseoSans-700Italic)
    // dan ada di FONT_MAP, gunakan langsung
    if (FONT_MAP[fontFamily]) {
      return fontFamily;
    }

    // Logic for MuseoSans
    if (fontFamily.includes('MuseoSans')) {
      const suffix = italic ? 'Italic' : '';
      const weightMap: Record<string, string> = {
        'normal': '300',
        'bold': '700',
        '300': '300',
        '500': '500',
        '700': '700',
        '900': '900',
      };
      const mappedWeight = weightMap[fontWeight] || '300'; // Default to 300 if not found
      key = `MuseoSans-${mappedWeight}${suffix}`;
    }
    // Logic for BookAntiqua
    else if (fontFamily === 'Book Antiqua' || fontFamily === 'BookAntiqua') {
      if (fontWeight === 'bold') {
        key = 'BookAntiqua-Bold';
      } else {
        key = 'BookAntiqua';
      }
    } else {
      // Fallback for other fonts not explicitly mapped, or if default Fabric.js font is used
      // This will use the original fontKey logic, which might not always match FONT_MAP directly
      // for non-custom fonts.
      key = fontKey(fontFamily, fontWeight, italic);
    }
    return key;
  }, []);

  // Fungsi untuk export dengan pdf-lib (lebih efisien) 
  const exportWithPdfLib = async (exportAllPages: boolean = false) => { 
    try { 
        if (canvas) {
          await saveCurrentPageEdits(currentPage);
        }
      // Gunakan salinan asli blankPdfBytes dari useRef
      const bytesToLoad = originalPdfBytesRef.current;
      if (!bytesToLoad) {
        console.error("Original PDF bytes not available for export.");
        alert('Terjadi kesalahan: Data PDF asli tidak tersedia untuk diekspor.');
        return;
      }

      console.log("Using originalPdfBytesRef for export:", bytesToLoad.slice(0, 10)); // Log first few bytes
      
      // Muat PDF asli dari salinan yang tidak dimodifikasi 
      const originalPdf = await PDFDocument.load(bytesToLoad); 
      const pdfDoc = await PDFDocument.create(); // Buat PDF baru 
      
      // REGISTER FONTKIT - This is the key fix!
      pdfDoc.registerFontkit(fontkit);
      
      const pagesToExport = exportAllPages ?  
        Array.from({ length: totalPages }, (_, i) => i + 1) :  
        [currentPage]; 

      // Cache untuk font yang sudah di-embed dalam sesi ekspor ini
      const embeddedFontCache: Record<string, any> = {}; 

      for (const pageNum of pagesToExport) { 
        // Salin halaman dari PDF asli 
        const [copiedPage] = await pdfDoc.copyPages(originalPdf, [pageNum - 1]); 
        const page = pdfDoc.addPage(copiedPage); 
        
        // Dapatkan dimensi halaman 
        const { width, height } = page.getSize(); 
        
        // Filter text blocks untuk halaman ini 
        const blocksForPage = allTextBlocks.filter(block => block.pageNumber === pageNum); 
        
        // Tambahkan teks yang sudah diedit 
        for (const block of blocksForPage) { 
          const textToRender = block.translated || block.text; 
          if (!textToRender) continue; 

          // Konversi koordinat (PDF koordinat dimulai dari kiri bawah) 
          const x = block.x; 
          const y = height - block.y;
          
          // Tentukan font (gunakan font built-in PDF untuk kompatibilitas) 
          let font; 
          const mappedFontKeyForPdfLib = getPdfLibFontKey(block);
          const fontBytes = pdfLibFontBytesRef.current[mappedFontKeyForPdfLib]; // Dapatkan bytes font yang telah dimuat

          if (fontBytes) {
            if (embeddedFontCache[mappedFontKeyForPdfLib]) {
              font = embeddedFontCache[mappedFontKeyForPdfLib];
            } else {
              try {
                font = await pdfDoc.embedFont(fontBytes);
                embeddedFontCache[mappedFontKeyForPdfLib] = font; // Cache font yang sudah di-embed
                console.log(`Successfully embedded custom font: ${mappedFontKeyForPdfLib}`);
              } catch (embedError) {
                console.warn(`Failed to embed custom font ${mappedFontKeyForPdfLib}, falling back to Helvetica:`, embedError);
                // Fallback ke Helvetica jika embedding gagal
                if (block.fontWeight === 'bold' && block.italic) { 
                  font = await pdfDoc.embedFont('Helvetica-BoldOblique'); 
                } else if (block.fontWeight === 'bold') { 
                  font = await pdfDoc.embedFont('Helvetica-Bold'); 
                } else if (block.italic) { 
                  font = await pdfDoc.embedFont('Helvetica-Oblique'); 
                } else { 
                  font = await pdfDoc.embedFont('Helvetica'); 
                } 
              }
            }
          } else {
            // Fallback ke Helvetica jika bytes font kustom tidak ditemukan atau tidak dimuat
            console.warn(`Custom font bytes for ${mappedFontKeyForPdfLib} not found or not loaded, falling back to Helvetica.`);
            if (block.fontWeight === 'bold' && block.italic) { 
              font = await pdfDoc.embedFont('Helvetica-BoldOblique'); 
            } else if (block.fontWeight === 'bold') { 
              font = await pdfDoc.embedFont('Helvetica-Bold'); 
            } else if (block.italic) { 
              font = await pdfDoc.embedFont('Helvetica-Oblique'); 
            } else { 
              font = await pdfDoc.embedFont('Helvetica'); 
            } 
          }
          
          // Jika teks mengandung newline, split dan render per baris
          if (textToRender.includes('\n')) {
            const lines = textToRender.split('\n');
            const lineHeight = block.lineHeight || block.size * 1.1; // Default 1.1 jika tidak ada
            
            lines.forEach((line, i) => {
              page.drawText(line, {
                x: x,
                y: y - (i * lineHeight), // Adjust Y position for each line
                size: block.size,
                font: font,
                color: rgb(block.color.r, block.color.g, block.color.b),
                rotate: degrees(block.rotation),
              });
            });
          } else {
            // Render teks biasa jika tidak ada newline
            page.drawText(textToRender, {
              x: x,
              y: y,
              size: block.size,
              font: font,
              color: rgb(block.color.r, block.color.g, block.color.b),
              rotate: degrees(block.rotation),
            });
          }
        } 
      } 

      // Serialize PDF 
      const pdfBytes = await pdfDoc.save(); 
      
      // Download 
      const fileName = exportAllPages ?  
        `${selectedPdfName}_all-pages_${targetLanguage}.pdf` :  
        `${selectedPdfName}_page-${currentPage}_${targetLanguage}.pdf`; 
      
      const blob = new Blob([new Uint8Array(pdfBytes)],{ type: 'application/pdf' }); 
      const url = URL.createObjectURL(blob); 
      const a = document.createElement('a'); 
      a.href = url; 
      a.download = fileName; 
      a.click(); 
      URL.revokeObjectURL(url); 

      console.log(`PDF exported successfully: ${fileName}`); 
    } catch (error) { 
      console.error('Error exporting PDF:', error); 
      alert('There is an error when exporting PDF. Please try again.'); 
    } 
  }; 

  // Fungsi untuk export semua halaman ke PDF 
  const exportAllPagesToPDF = async () => {
    setIsExporting(true);
    exportWithPdfLib(true);
    try {
      let blocksToExport = allTextBlocks; // Gunakan state awal
      if (canvas) {
          // Simpan editan halaman saat ini dan dapatkan data terbarunya
          const latestEdits = await saveCurrentPageEdits(currentPage);

          // Gabungkan data terbaru dengan state yang ada
          const otherPagesBlocks = allTextBlocks.filter(b => b.pageNumber !== currentPage);
          blocksToExport = [...otherPagesBlocks, ...latestEdits.filter(b => b.pageNumber === currentPage)];
      }

      // Buat PDF bytes seperti biasa
      const bytesToLoad = originalPdfBytesRef.current;
      if (!bytesToLoad) {
        console.error("Original PDF bytes not available for export.");
        throw new Error('Data PDF asli tidak tersedia untuk diekspor.');
      }

      const originalPdf = await PDFDocument.load(bytesToLoad);
      const pdfDoc = await PDFDocument.create();
      pdfDoc.registerFontkit(fontkit);

      const pagesToExport = Array.from({ length: totalPages }, (_, i) => i + 1);
      const embeddedFontCache: Record<string, any> = {};

      // Proses semua halaman (kode yang sama seperti sebelumnya)
      for (const pageNum of pagesToExport) {
        const [copiedPage] = await pdfDoc.copyPages(originalPdf, [pageNum - 1]);
        const page = pdfDoc.addPage(copiedPage);
        const { width, height } = page.getSize();
        const blocksForPage = blocksToExport.filter(block => block.pageNumber === pageNum);

        for (const block of blocksForPage) {
          const textToRender = block.translated || block.text;
          if (!textToRender) continue;

          const x = block.x;
          const y = height - block.y;
          
          let font;
          const mappedFontKeyForPdfLib = getPdfLibFontKey(block);
          const fontBytes = pdfLibFontBytesRef.current[mappedFontKeyForPdfLib];

          if (fontBytes) {
            if (embeddedFontCache[mappedFontKeyForPdfLib]) {
              font = embeddedFontCache[mappedFontKeyForPdfLib];
            } else {
              try {
                font = await pdfDoc.embedFont(fontBytes);
                embeddedFontCache[mappedFontKeyForPdfLib] = font;
              } catch (embedError) {
                console.warn(`Failed to embed custom font ${mappedFontKeyForPdfLib}, falling back to Helvetica:`, embedError);
                if (block.fontWeight === 'bold' && block.italic) {
                  font = await pdfDoc.embedFont('Helvetica-BoldOblique');
                } else if (block.fontWeight === 'bold') {
                  font = await pdfDoc.embedFont('Helvetica-Bold');
                } else if (block.italic) {
                  font = await pdfDoc.embedFont('Helvetica-Oblique');
                } else {
                  font = await pdfDoc.embedFont('Helvetica');
                }
              }
            }
          } else {
            if (block.fontWeight === 'bold' && block.italic) {
              font = await pdfDoc.embedFont('Helvetica-BoldOblique');
            } else if (block.fontWeight === 'bold') {
              font = await pdfDoc.embedFont('Helvetica-Bold');
            } else if (block.italic) {
              font = await pdfDoc.embedFont('Helvetica-Oblique');
            } else {
              font = await pdfDoc.embedFont('Helvetica');
            }
          }

          if (textToRender.includes('\n')) {
            const lines = textToRender.split('\n');
            const lineHeight = block.lineHeight || block.size * 1.1;
            
            lines.forEach((line, i) => {
              page.drawText(line, {
                x: x,
                y: y - (i * lineHeight),
                size: block.size,
                font: font,
                color: rgb(block.color.r, block.color.g, block.color.b),
                rotate: degrees(block.rotation),
              });
            });
          } else {
            page.drawText(textToRender, {
              x: x,
              y: y,
              size: block.size,
              font: font,
              color: rgb(block.color.r, block.color.g, block.color.b),
              rotate: degrees(block.rotation),
            });
          }
        }
      }

      // Serialize PDF
      const pdfBytes = await pdfDoc.save();
      
      // Buat FormData untuk upload
      const formData = new FormData();
      const pdfBlob = new Blob([new Uint8Array(pdfBytes)], { type: 'application/pdf' });
      const fileName = `${selectedPdfName}_${targetLanguage}.pdf`;
      
      formData.append('namaBuku', selectedPdfName);
      formData.append('language', targetLanguage);
      formData.append('translatedPdf', pdfBlob, fileName);

      // Upload ke database
      const response = await fetch('/api/books', {
        method: 'PUT',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Translation uploaded successfully:', result);
        showNotification('success', `The PDF has been successfully translated and saved to the database!`);
      } else {
        const error = await response.json();
        console.error('Upload failed:', error);
        showNotification('error', `Gagal mengupload PDF: ${error.error}`);
      }

    } catch (error) {
      console.error('Error exporting and uploading PDF:', error);
      const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat mengexport dan mengupload PDF. Silakan coba lagi.';
      showNotification('error', errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  // Loading Screen Component
  const LoadingScreen = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <h3 className="text-lg font-semibold mb-2">Exporting boo...</h3>
          <p className="text-gray-600 text-center">
            Processing and uploading the translated PDF to the database. Please wait a moment.
          </p>
        </div>
      </div>
    </div>
  );

  // Notification Component
  const NotificationToast = () => {
    if (!notification.type) return null;

    const bgColor = notification.type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = notification.type === 'success' ? '✓' : '✕';

    return (
      <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
        <div className={`${bgColor} text-white px-8 py-6 rounded-xl shadow-2xl flex items-center gap-4 min-w-96 max-w-md pointer-events-auto transform transition-all duration-300 scale-100`}>
          <span className="text-lg font-bold">{icon}</span>
          <span>{notification.message}</span>
          <button
            onClick={() => setNotification({ type: null, message: '' })}
            className="ml-auto text-white hover:text-gray-200"
          >
            <span className="text-xl">×</span>
          </button>
        </div>
      </div>
    );
  };

  // Helper function untuk menampilkan notifikasi
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    
    // Auto hide notifikasi setelah 5 detik
    setTimeout(() => {
      setNotification({ type: null, message: '' });
    }, 5000);
  };

 return ( 
    <div className="flex flex-col h-full overflow-auto"> 
      {/* Header/Toolbar - Sticky */} 
      {/* Loading Screen */}
      {isExporting && <LoadingScreen />}
      
      {/* Notification Toast */}
      <NotificationToast />
      <div className="sticky top-0 z-10 bg-gray-100 p-3 border-b border-gray-200 flex flex-wrap items-center justify-start gap-4 flex-shrink-0"> 
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">Editor View</h2>
        </div> 

        {/* Toolbar controls for selected object */} 
        <div className="flex flex-wrap items-center gap-3">
            <Button onClick={addTextbox} variant="outline" className="h-8 w-8 p-0">
                <PlusSquare className="h-4 w-4" />
            </Button>
            <Button
                onClick={deleteSelectedObject}
                variant="destructive"
                className="h-8 w-8 p-0"
                disabled={!selectedObject}
            >
                <Trash2 className="h-4 w-4" />
            </Button>
            {selectedObject && ( 
            <>
            <div> 
              <Label htmlFor="font-size-input" className="sr-only">Ukuran Font</Label> 
              <Input 
                id="font-size-input" 
                type="number" 
                value={toolbarState.fontSize || ''} 
                onChange={handleFontSizeChange} 
                onBlur={handleFontSizeBlur} 
                className="w-24 h-8 text-sm" 
                aria-label="Ukuran Font" 
                min="1" 
                placeholder="Size" 
              /> 
            </div> 
            <div>
              <Label htmlFor="font-family-select" className="sr-only">Gaya Font</Label>
              <Select
                value={toolbarState.fontFamily.split('-')[0]} // Tampilkan hanya nama font dasar
                onValueChange={(value) => {
                  // Ketika user memilih font, gunakan weight dan italic yang sedang aktif
                  const actualFontKey = fontKey(value, toolbarState.fontWeight, toolbarState.fontStyle === 'italic');
                  const actualFontFamily = FONT_MAP[actualFontKey] ? actualFontKey : value;

                  // Jika menggunakan font file khusus, set Fabric.js weight dan style ke normal
                  const fabricWeight = FONT_MAP[actualFontKey] ? "normal" : toolbarState.fontWeight;
                  const fabricStyle = FONT_MAP[actualFontKey] ? "normal" : (toolbarState.fontStyle as "normal" | "italic");

                  setToolbarState(prev => ({ ...prev, fontFamily: actualFontFamily }));
                  updateSelectedText("fontFamily", actualFontFamily);
                  updateSelectedText("fontWeight", fabricWeight);
                  updateSelectedText("fontStyle", fabricStyle);
                }}
              >
                <SelectTrigger id="font-family-select" className="w-[180px] h-8 text-sm" aria-label="Gaya Font">
                  <SelectValue placeholder="Pick font">
                    {(() => {
                      const baseFontName = toolbarState.fontFamily.split('-')[0];
                      let displayFontFamily = baseFontName;
                      if (baseFontName === 'MuseoSans') {
                        // Gunakan font file yang tepat untuk preview
                        displayFontFamily = toolbarState.fontFamily;
                      }
                      return (
                        <span style={{ fontFamily: displayFontFamily, fontSize: "14px" }}>
                          {baseFontName}
                        </span>
                      );
                    })()}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {getAvailableFonts().map((font) => {
                    // Untuk preview di dropdown, gunakan font file yang tepat
                    let previewFontFamily = font.value;
                    if (font.value === 'MuseoSans') {
                      // Gunakan MuseoSans-300 sebagai preview default untuk MuseoSans
                      previewFontFamily = 'MuseoSans-300';
                    }

                    return (
                      <SelectItem key={font.value} value={font.value}>
                        <span style={{ fontFamily: previewFontFamily, fontSize: "14px" }}>{font.label}</span>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div>
              <div>
                <Label htmlFor="font-weight-select" className="sr-only">Font Weight</Label>
                <Select
                  value={toolbarState.fontWeight}
                  onValueChange={(value) => {
                    // Ketika weight berubah, update font family dengan file yang tepat
                    const baseFontName = toolbarState.fontFamily.split('-')[0];
                    const actualFontKey = fontKey(baseFontName, value, toolbarState.fontStyle === 'italic');
                    const actualFontFamily = FONT_MAP[actualFontKey] ? actualFontKey : baseFontName;

                    // Jika menggunakan font file khusus, set Fabric.js weight ke normal
                    const fabricWeight = FONT_MAP[actualFontKey] ? "normal" : value;

                    setToolbarState(prev => ({ ...prev, fontWeight: value, fontFamily: actualFontFamily }));
                    updateSelectedText("fontWeight", fabricWeight);
                    updateSelectedText("fontFamily", actualFontFamily);
                  }}
                >
                  <SelectTrigger
                    id="font-weight-select"
                    className="w-24 h-8 text-sm"
                    aria-label="Font Weight"
                  >
                    <SelectValue placeholder="Pick Weight" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="bold" className="font-bold">Bold</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label
                htmlFor="font-style-checkbox"
                className="text-xs flex items-center gap-2 cursor-pointer"
              >
                <Checkbox
                  id="font-style-checkbox"
                  checked={toolbarState.fontStyle === "italic"}
                  onCheckedChange={(checked) => {
                    const newStyle = checked ? "italic" : "normal";
                    // Ketika italic berubah, update font family dengan file yang tepat
                    const baseFontName = toolbarState.fontFamily.split('-')[0];
                    const actualFontKey = fontKey(baseFontName, toolbarState.fontWeight, !!checked);
                    const actualFontFamily = FONT_MAP[actualFontKey] ? actualFontKey : baseFontName;

                    // Jika menggunakan font file khusus, set Fabric.js style ke normal
                    const fabricStyle = FONT_MAP[actualFontKey] ? "normal" : newStyle;

                    setToolbarState(prev => ({ ...prev, fontStyle: newStyle, fontFamily: actualFontFamily }));
                    updateSelectedText("fontStyle", fabricStyle);
                    updateSelectedText("fontFamily", actualFontFamily);
                  }}
                />
                Italic
              </Label>
            </div>
            <div> 
              <Label htmlFor="color-input" className="sr-only">Warna</Label> 
              <Input 
                id="color-input" 
                type="color" 
                value={toolbarState.fill} 
                onChange={(e) => { 
                  setToolbarState(prev => ({ ...prev, fill: e.target.value })); 
                  updateSelectedText("fill", e.target.value); 
                }} 
                className="w-12 h-8" 
                aria-label="Warna" 
              /> 
            </div> 
            <Button 
            onClick={toggleTextVersion} 
            variant="outline" 
            className="text-xs h-8" 
            disabled={ 
            !selectedObject ||
            ((selectedObject.data as any)?.originalText === (selectedObject.data as any)?.translatedText) 
            } 
            > 
            {toolbarState.isOriginalText ? 'Change to Translate' : 'Change to Original'} 
            </Button> 
            </>
            )} 
            </div>

        {/* Export Buttons */} 
        <div className="flex items-center gap-2 ml-auto">
          <Button
            onClick={exportAllPagesToPDF}
            variant="default"
            disabled={!canvas || totalPages <= 1 || isExporting}
            className="h-8 text-sm"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Export Pages and Submit
              </>
            )}
          </Button>
        </div>
      </div> 
    
    {/* Main Content Area - Flex grow to fill remaining space */}
    <div className="flex-1 min-h-0 flex p-6 gap-6">
      <div className="flex-1 border border-gray-200 shadow-lg bg-white rounded-xl hover:shadow-2xl hover:scale-[1.005] transition-all duration-300 flex flex-col">
        <div className="bg-blue-600 text-white px-4 py-2 text-sm font-semibold text-center rounded-t-xl flex-shrink-0">
          Original PDF
        </div>
        <div className="flex-1 overflow-auto">
          <div className="p-4 py-8 flex items-center justify-center min-h-full">
            <canvas 
              ref={originalCanvasRef}
              className="w-auto h-auto shadow-md"
              style={{ boxShadow: '0 0 8px rgba(0,0,0,0.1)' }}
            />
          </div>
        </div>
      </div>

      <div className="flex-1 border border-gray-200 shadow-lg bg-white rounded-xl hover:shadow-2xl hover:scale-[1.005] transition-all duration-300 flex flex-col">
        <div className="bg-green-600 text-white px-4 py-2 text-sm font-semibold text-center rounded-t-xl flex-shrink-0">
          Editor
        </div>
        <div className="flex-1 overflow-auto">
          <div className="p-4 py-8 flex items-center justify-center min-h-full">
            <canvas 
              ref={canvasRef}
              className="shadow-md"
              style={{ boxShadow: '0 0 8px rgba(0,0,0,0.1)' }}
            />
          </div>
        </div>
      </div>
    </div>
      
      {/* Footer for Page Navigation - Sticky */} 
      <div className="sticky bottom-0 z-10 bg-gray-100 p-3 border-t border-gray-200 flex items-center justify-center gap-4 flex-shrink-0"> 
        <Button onClick={() => changePage(currentPage - 1)} disabled={currentPage <= 1} className="h-8 w-8 p-0"> 
          &lt; 
        </Button> 
        <div className="flex items-center gap-1"> 
          <Label htmlFor="page-input" className="sr-only">Halaman</Label> 
          <Input 
            id="page-input" 
            type="number" 
            value={pageInput} 
            onChange={handlePageInputChange} 
            onBlur={handlePageInputBlur} 
            onKeyDown={handlePageInputKeyDown} 
            className="w-16 h-8 text-sm text-center" 
            min="1" 
            max={totalPages} 
            aria-label="Input Halaman" 
          /> 
          <span className="text-sm font-medium whitespace-nowrap"> 
            dari {totalPages} 
          </span> 
        </div> 
        <Button onClick={() => changePage(currentPage + 1)} disabled={currentPage >= totalPages} className="h-8 w-8 p-0"> 
          &gt; 
        </Button> 
      </div> 
    </div> 
  );
}