// utils/fontMapper.ts
export const FONT_MAP: Record<string, string> = {
  'BookAntiqua':            '/fonts/bookantiqua.ttf',
  'BookAntiqua-Bold':       '/fonts/bookantiqua_bold.ttf',
  'MuseoSans-100':          '/fonts/MuseoSans-100.otf',
  'MuseoSans-100Italic':    '/fonts/MuseoSans-100Italic.otf',
  'MuseoSans-300':          '/fonts/MuseoSans-300.otf',
  'MuseoSans-300Italic':    '/fonts/MuseoSans-300Italic.otf',
  'MuseoSans-500':          '/fonts/MuseoSans-500.otf',
  'MuseoSans-500Italic':    '/fonts/MuseoSans-500Italic.otf',
  'MuseoSans-700':          '/fonts/MuseoSans-700.otf',
  'MuseoSans-700Italic':    '/fonts/MuseoSans-700Italic.otf',
  'MuseoSans-900':          '/fonts/MuseoSans-900.otf',
  'MuseoSans-900Italic':    '/fonts/MuseoSans-900Italic.otf',
};

// build a key from the extracted block
export const fontKey = (family: string, weight: string, italic: boolean) => {
  const suffix = italic ? 'Italic' : '';

  // Mapping weight untuk MuseoSans sesuai permintaan:
  // normal -> MuseoSans-300
  // bold -> MuseoSans-700
  let w = '300'; // default normal
  if (weight === 'bold') {
    w = '700';
  } else if (weight === '600') {
    w = '500';
  } else if (weight === '900') {
    w = '900';
  } else if (weight === '100') {
    w = '100';
  } else if (weight === '500') {
    w = '500';
  } else if (weight === '700') {
    w = '700';
  } else if (weight === '300') {
    w = '300';
  }

  return `${family}-${w}${suffix}`;
};