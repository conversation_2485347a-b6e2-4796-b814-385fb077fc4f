// utils/fontMapper.ts
export const FONT_MAP: Record<string, string> = {
  'BookAntiqua':            '/fonts/bookantiqua.ttf',
  'BookAntiqua-Bold':       '/fonts/bookantiqua_bold.ttf',
  'MuseoSans-100':          '/fonts/MuseoSans-100.otf',
  'MuseoSans-100Italic':    '/fonts/MuseoSans-100Italic.otf',
  'MuseoSans-300':          '/fonts/MuseoSans-300.otf',
  'MuseoSans-300Italic':    '/fonts/MuseoSans-300Italic.otf',
  'MuseoSans-500':          '/fonts/MuseoSans-500.otf',
  'MuseoSans-500Italic':    '/fonts/MuseoSans-500Italic.otf',
  'MuseoSans-700':          '/fonts/MuseoSans-700.otf',
  'MuseoSans-700Italic':    '/fonts/MuseoSans-700Italic.otf',
  'MuseoSans-900':          '/fonts/MuseoSans-900.otf',
  'MuseoSans-900Italic':    '/fonts/MuseoSans-900Italic.otf',
};

// build a key from the extracted block
export const fontKey = (family: string, weight: string, italic: boolean) => {
  const suffix = italic ? 'Italic' : '';
  const w = weight === 'bold' ? '700' : weight === '600' ? '500' : '300';
  return `${family}-${w}${suffix}`;
};