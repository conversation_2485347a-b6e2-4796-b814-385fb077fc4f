'use client';

import { useState } from "react";
import { books } from "@/lib/data/books";
import HeroSection from "@/components/landingpage/HeroSection";
import BookGrid from "@/components/landingpage/BookGrid";
import FeaturesSection from "@/components/landingpage/FeaturesSection";
import CallToActionSection from "@/components/landingpage/CallToActionSection";
import Navbar from "@/components/navbar/Navbar";
import KidsPDFViewer from "@/components/KidsPdfViewer";

export default function HomePage() {
  const [selectedBook, setSelectedBook] = useState<any>(null);
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  const handleStartReading = (bookId: number) => {
    const book = books.find((b) => b.id === bookId);
    if (book?.pdfPath) {
      setSelectedBook(book);
      setShowPDFViewer(true);
    }
  };

  const scrollToBooks = () => {
    const booksSection = document.getElementById('books');
    if (booksSection) {
      booksSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-1">
        <HeroSection onCTA={scrollToBooks} ctaLink="" />
        <BookGrid books={books} onBookAction={handleStartReading} />
        <FeaturesSection />
        <CallToActionSection onCTA={scrollToBooks} />
      </main>

      {/* PDF Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={selectedBook.pdfPath}
          bookTitle={selectedBook.title}
          onClose={handleClosePDFViewer}
        />
      )}
    </div>
  );
}
