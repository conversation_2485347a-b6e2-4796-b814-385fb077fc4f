import React from 'react';
import Image from 'next/image';
import { Book } from '@/lib/data/books';
import { StarRating } from './StarRating';

interface BookCardProps {
  book: Book;
  onStartReading?: (bookId: number) => void;
  onDownload?: (bookId: number) => void;
  onSubscription?: (bookId: number) => void;
  onDownloadPreview?: (bookId: number) => void;
  isOwned?: boolean;
}

export default function BookCard({ 
  book, 
  onStartReading, 
  onDownload,
  onSubscription,
  onDownloadPreview,
  isOwned = false 
}: BookCardProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className="bg-white rounded-2xl overflow-hidden">
      <div className="flex flex-col md:flex-row gap-6 p-6">
        {/* Book Cover - Increased size and centered on mobile */}
        <div className="flex-shrink-0 flex justify-center md:justify-start">
          <div className="w-96 h-96 relative">
            <div className="absolute inset-0"></div>
            <div className="absolute inset-0 flex items-center justify-center p-3">
              {book.coverImage ? (
                <Image 
                  src={book.coverImage} 
                  alt={`Cover of ${book.title}`}
                  width={384}
                  height={384}
                  className="max-w-full max-h-full object-contain"
                  priority
                />
              ) : (
                <div className="text-6xl">📖</div>
              )}
            </div>
            <Image 
              src="/thumbnails/thumbnail-border.svg" 
              alt=""
              width={384}
              height={384}
              className="absolute inset-0 w-full h-full object-fill pointer-events-none z-20"
            />
            {/* Owned Badge */}
            {isOwned && (
              <div className="absolute top-2 right-2 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-30">
                Owned
              </div>
            )}
          </div>
        </div>

        {/* Details */}
        <div className="flex-1 text-center md:text-left">
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            {book.title}
          </h3>

          <div className="mb-4 flex justify-center md:justify-start">
            <StarRating rating={book.rating} size={36} />
          </div>

          <div className="mb-4 space-y-2 text-gray-600 font-quicksand font-medium">
            <p><strong>Author:</strong> {book.author}</p>
            <p><strong>Category:</strong> Cerita Anak</p>
            <p><strong>Pages:</strong> {book.pages} halaman</p>
            <p><strong>License:</strong> {book.license}</p>
          </div>

          <p className="text-lg text-gray-700 mb-6 leading-relaxed font-quicksand font-medium">
            {book.description}
          </p>

          {/* Action Buttons - Different for owned vs available books */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start items-center">
            {isOwned ? (
              // Buttons for owned books
              <>
                <button
                  onClick={() => onStartReading && onStartReading(book.id)}
                  className="relative w-[150px] h-[56px]"
                  aria-label={`Read ${book.title}`}
                >
                  <Image
                    src="/buttons/preview-button.svg"
                    alt=""
                    width={150}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Read Now
                  </span>
                </button>

                {/* <button
                  onClick={() => onDownload && onDownload(book.id)}
                  className="relative w-[220px] h-[56px]"
                  aria-label={`Download ${book.title}`}
                >
                  <Image
                    src="/buttons/download-button.svg"
                    alt=""
                    width={220}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Download
                  </span>
                </button> */}
              </>
            ) : (
              // Buttons for available books
              <>
                <button
                  onClick={() => onStartReading && onStartReading(book.id)}
                  className="relative w-[150px] h-[56px]"
                  aria-label={`Start reading ${book.title}`}
                >
                  <Image
                    src="/buttons/preview-button.svg"
                    alt=""
                    width={150}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Preview
                  </span>
                </button>

                <button 
                  onClick={() => onSubscription && onSubscription(book.id)}
                  className="relative w-[150px] h-[56px]"
                  aria-label={`Subscription ${book.title}`}
                >
                  <Image
                    src="/buttons/purchase-button.svg"
                    alt=""
                    width={150}
                    height={56}
                    className="absolute inset-0"
                  />
                  <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                    Pick This Book
                  </span>
                </button>
              </>
            )}
          </div>

          {/* Download Preview Button - Only for available books */}
          {!isOwned && (
            <div className="mt-6 flex justify-center md:justify-start">
              <button 
                onClick={() => onDownloadPreview && onDownloadPreview(book.id)}
                className="relative w-[220px] h-[56px]"
                aria-label={`Download ${book.title} as PDF`}
              >
                <Image
                  src="/buttons/download-button.svg"
                  alt=""
                  width={220}
                  height={56}
                  className="absolute inset-0"
                />
                <span className="absolute left-2 right-2 top-1/2 -translate-y-1/2 flex items-center justify-center text-black font-bold font-quicksand text-lg">
                  Download PDF Preview
                </span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}