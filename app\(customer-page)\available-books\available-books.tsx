'use client';

import React, { useState } from 'react';
import BookCard from '@/components/landingpage/BookCard';
import Pagination from '@/components/landingpage/Pagination';
import { books } from '@/lib/data/books';
import KidsPDFViewer from '@/components/KidsPdfViewer';

interface AvailableBooksProps {
  booksPerPage?: number;
}

export default function AvailableBooks({ booksPerPage = 3 }: AvailableBooksProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBook, setSelectedBook] = useState<any>(null);
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  // Pagination logic
  const totalPages = Math.ceil(books.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = books.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    document.getElementById('books')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handlePreview = (bookId: number) => {
    const book = books.find(b => b.id === bookId);
    if (book?.pdfPath) {
      setSelectedBook(book);
      setShowPDFViewer(true);
    }
    console.log('Preview book ID:', bookId);
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  const handleSubscription = (bookId: number) => {
    alert(`Memproses langganan buku ID: ${bookId}`);
    console.log('Subscription book ID:', bookId);
  };

  const handleDownloadPreview = (bookId: number) => {
    const book = books.find(b => b.id === bookId);
    if (book?.pdfPath) {
      const link = document.createElement('a');
      link.href = book.pdfPath;
      link.download = `${book.title}-preview.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    console.log('Download preview for book ID:', bookId);
  };

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 py-8">
        <section id="books" className="space-y-6">
          {currentBooks.map((book) => (
            <BookCard
              key={book.id}
              book={book}
              onStartReading={handlePreview}
              onSubscription={handleSubscription}
              onDownloadPreview={handleDownloadPreview}
              isOwned={false}
            />
          ))}
        </section>

        {totalPages > 1 && (
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* KidsPDFViewer Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={selectedBook.pdfPath}
          bookTitle={selectedBook.title}
          onClose={handleClosePDFViewer}
        />
      )}
    </>
  );
}
