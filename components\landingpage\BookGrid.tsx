import React, { useState } from 'react';
import BookCard from './BookCard';
import Pagination from './Pagination';
import { Book } from '@/lib/data/books';

interface BookGridProps {
  books: Book[];
  onBookAction: (bookId: number) => void;
}

export default function BookGrid({ books, onBookAction }: BookGridProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const booksPerPage = 3;

  // Calculate pagination values
  const totalPages = Math.ceil(books.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = books.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of books section when page changes
    document.getElementById('books')?.scrollIntoView({ behavior: 'smooth' });
  };

  if (books.length === 0) {
    return (
      <section id="books" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              What kind of book you are looking for?
            </h2>
            <p 
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              Kindly find and explore below.
            </p>
          </div>
          
          <div className="text-center">
            <div className="text-6xl mb-4">📖</div>
            <p 
              className="text-xl text-gray-600"
              style={{ fontFamily: 'Quicksand, sans-serif' }}
            >
              No books available at the moment. Check back soon for new adventures!
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="books" className="min-h-screen w-full">
      <div className="w-full h-full p-4 md:p-8">
        <div className="text-center mb-8 md:mb-12">
          <h2 
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            style={{ fontFamily: 'Quicksand, sans-serif' }}
          >
            What kind of book you are looking for?
          </h2>
          <p 
            className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto"
            style={{ fontFamily: 'Quicksand, sans-serif' }}
          >
            Kindly find and explore below.
          </p>
        </div>
        
        <div className="w-full space-y-6 md:space-y-8">
          {currentBooks.map((book) => (
            <BookCard 
              key={book.id} 
              book={book} 
              onStartReading={onBookAction}
            />
          ))}
        </div>

        {/* Pagination Component */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mb-8"
          />
        )}
      </div>
    </section>
  );
}