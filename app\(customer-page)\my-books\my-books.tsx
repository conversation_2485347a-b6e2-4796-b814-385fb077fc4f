'use client';

import React, { useState } from 'react';
import BookCard from '@/components/landingpage/BookCard';
import Pagination from '@/components/landingpage/Pagination';
import KidsPDFViewer from '@/components/KidsPdfViewer';
import { myBooks } from '@/lib/data/my-books';

interface MyBooksProps {
  booksPerPage?: number;
}

export default function MyBooks({ booksPerPage = 3 }: MyBooksProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedBook, setSelectedBook] = useState<any>(null);
  const [showPDFViewer, setShowPDFViewer] = useState(false);

  // Calculate pagination values
  const totalPages = Math.ceil(myBooks.length / booksPerPage);
  const startIndex = (currentPage - 1) * booksPerPage;
  const endIndex = startIndex + booksPerPage;
  const currentBooks = myBooks.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    document.getElementById('my-books')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleReadBook = (bookId: number) => {
    const book = myBooks.find(b => b.id === bookId);
    if (book?.pdfPath) {
      setSelectedBook(book);
      setShowPDFViewer(true);
    }
    console.log('Reading book ID:', bookId);
  };

  const handleClosePDFViewer = () => {
    setShowPDFViewer(false);
    setSelectedBook(null);
  };

  const handleDownloadBook = (bookId: number) => {
    const book = myBooks.find(b => b.id === bookId);
    if (book?.pdfPath) {
      // Create a download link
      const link = document.createElement('a');
      link.href = book.pdfPath;
      link.download = `${book.title}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    console.log('Downloading book ID:', bookId);
  };

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4 font-quicksand">
            My Books
          </h1>
          <p className="text-lg text-gray-600 font-quicksand">
            Books Collection
          </p>
        </div>

        {/* Books List */}
        <section id="my-books" className="space-y-6">
          {currentBooks.length > 0 ? (
            currentBooks.map((book) => (
              <BookCard 
                key={book.id} 
                book={book} 
                onStartReading={handleReadBook}
                onDownload={handleDownloadBook}
                isOwned={true}
              />
            ))
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📚</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2 font-quicksand">
                Book is empty
              </h3>
              <p className="text-gray-600 font-quicksand">
                Find out your books.
              </p>
            </div>
          )}
        </section>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* PDF Viewer Modal */}
      {showPDFViewer && selectedBook && (
        <KidsPDFViewer
          pdfUrl={selectedBook.pdfPath}
          bookTitle={selectedBook.title}
          onClose={handleClosePDFViewer}
        />
      )}
    </>
  );
}